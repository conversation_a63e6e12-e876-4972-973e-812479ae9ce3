<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8-bit Dropdown Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Pixelify+Sans:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Pixelify Sans', monospace;
            background: #f5f5dc;
            color: #2d4a2b;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #2d4a2b;
        }
        
        .demo-section {
            margin: 2rem 0;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.5);
            border: 4px solid #2d4a2b;
            border-radius: 0;
        }
        
        /* Button Styles */
        .btn {
            font-family: 'Pixelify Sans', monospace;
            padding: 0.5rem 1rem;
            border: 2px solid #2d4a2b;
            background: #8fbc8f;
            color: #2d4a2b;
            cursor: pointer;
            border-radius: 0;
            position: relative;
            transition: all 0.15s ease;
            margin: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }
        
        .btn:active {
            transform: translateY(0);
            box-shadow: 1px 1px 0px rgba(0,0,0,0.3);
        }
        
        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
            margin: 1rem;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            background: #f5f5dc;
            min-width: 200px;
            border: 4px solid #2d4a2b;
            z-index: 1;
            padding: 0.5rem;
            top: 100%;
            left: 0;
            box-shadow: 8px 8px 0px rgba(0,0,0,0.3);
        }
        
        .dropdown:hover .dropdown-content {
            display: block;
        }
        
        .dropdown-item {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            transition: all 0.15s ease;
            overflow: hidden;
            margin: 2px 0;
            background: transparent;
        }
        
        .dropdown-item:hover {
            background: #8fbc8f;
            transform: translateY(-1px);
        }
        
        .dropdown-item:active {
            transform: translateY(0);
        }
        
        /* Pixel Border Effects for Dropdown Items */
        .dropdown-item::before {
            content: '';
            position: absolute;
            inset: 0;
            opacity: 0;
            transition: opacity 0.15s ease;
            pointer-events: none;
        }
        
        .dropdown-item:hover::before {
            opacity: 1;
        }
        
        /* Top and bottom borders */
        .dropdown-item::after {
            content: '';
            position: absolute;
            top: -2px;
            left: 4px;
            right: 4px;
            height: 2px;
            background: linear-gradient(90deg, 
                #2d4a2b 0%, #2d4a2b 50%, transparent 50%, transparent 52%, 
                #2d4a2b 52%, #2d4a2b 100%);
            opacity: 0;
            transition: opacity 0.15s ease;
        }
        
        .dropdown-item:hover::after {
            opacity: 1;
        }
        
        /* Side borders using box-shadow */
        .dropdown-item:hover {
            box-shadow: 
                -2px 0 0 #2d4a2b,
                2px 0 0 #2d4a2b,
                0 -2px 0 #2d4a2b,
                0 2px 0 #2d4a2b;
        }
        
        .separator {
            height: 2px;
            background: linear-gradient(90deg, 
                transparent 0%, transparent 10%, 
                #2d4a2b 10%, #2d4a2b 90%, 
                transparent 90%, transparent 100%);
            margin: 0.5rem 0;
        }
        
        .shortcut {
            float: right;
            font-size: 0.75rem;
            background: rgba(45, 74, 43, 0.1);
            padding: 2px 6px;
            border: 1px solid #2d4a2b;
            margin-left: 1rem;
        }
        
        /* Theme variants */
        .city-theme {
            background: #f0f8ff;
            color: #1e3a8a;
        }
        
        .city-theme .btn {
            background: #3b82f6;
            color: white;
            border-color: #1e3a8a;
        }
        
        .city-theme .dropdown-content {
            background: #f0f8ff;
            border-color: #1e3a8a;
        }
        
        .city-theme .dropdown-item:hover {
            background: #3b82f6;
            color: white;
            box-shadow: 
                -2px 0 0 #1e3a8a,
                2px 0 0 #1e3a8a,
                0 -2px 0 #1e3a8a,
                0 2px 0 #1e3a8a;
        }
        
        .dungeon-theme {
            background: #2d1b1b;
            color: #d4af37;
        }
        
        .dungeon-theme .btn {
            background: #8b4513;
            color: #d4af37;
            border-color: #654321;
        }
        
        .dungeon-theme .dropdown-content {
            background: #2d1b1b;
            border-color: #654321;
        }
        
        .dungeon-theme .dropdown-item:hover {
            background: #8b4513;
            color: #d4af37;
            box-shadow: 
                -2px 0 0 #654321,
                2px 0 0 #654321,
                0 -2px 0 #654321,
                0 2px 0 #654321;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 8-bit Dropdown Components</h1>
        <p>Hover over the buttons to see the pixel border effects!</p>
        
        <div class="demo-section">
            <h2>Forest Theme (Default)</h2>
            <div class="dropdown">
                <button class="btn">My Account ▼</button>
                <div class="dropdown-content">
                    <div class="dropdown-item">👤 Profile <span class="shortcut">⇧⌘P</span></div>
                    <div class="dropdown-item">💳 Billing</div>
                    <div class="dropdown-item">⚙️ Settings</div>
                    <div class="separator"></div>
                    <div class="dropdown-item">📧 Support</div>
                    <div class="dropdown-item">🚪 Logout</div>
                </div>
            </div>
            
            <div class="dropdown">
                <button class="btn">Actions ▼</button>
                <div class="dropdown-content">
                    <div class="dropdown-item">➕ New Item</div>
                    <div class="dropdown-item">📝 Edit</div>
                    <div class="dropdown-item">📋 Copy</div>
                    <div class="dropdown-item">🗑️ Delete</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section city-theme">
            <h2>City Theme</h2>
            <div class="dropdown">
                <button class="btn">Cyber Menu ▼</button>
                <div class="dropdown-content">
                    <div class="dropdown-item">🔌 Connect</div>
                    <div class="dropdown-item">💾 Download</div>
                    <div class="dropdown-item">🌐 Network</div>
                    <div class="separator"></div>
                    <div class="dropdown-item">⚡ Power Off</div>
                </div>
            </div>
        </div>
        
        <div class="demo-section dungeon-theme">
            <h2>Dungeon Theme</h2>
            <div class="dropdown">
                <button class="btn">Inventory ▼</button>
                <div class="dropdown-content">
                    <div class="dropdown-item">⚔️ Weapons</div>
                    <div class="dropdown-item">🛡️ Armor</div>
                    <div class="dropdown-item">🧪 Potions</div>
                    <div class="separator"></div>
                    <div class="dropdown-item">💰 Gold: 1,250</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
